import { prisma } from '$lib/server/prisma';
import type { RequestHandler } from '../$types.js';
import { RedisConnection } from '$lib/server/redis.js';
import { v4 as uuid } from 'uuid';
import { uploadDocumentToR2 } from '$lib/utils/r2DocumentUpload';
import { ensureUniqueDocumentName } from '$lib/utils/documentNameUniqueness';
import { determineDocumentSource } from '$lib/utils/documentSource';
import { trackResumeCreation, canCreateResume } from '$lib/server/resume-usage';

// Using the shared Prisma client from $lib/server/prisma
const JOB_STREAM = 'resume-parsing::stream'; // Redis stream for resume parsing jobs (must match worker's expected stream name)
const JOB_GROUP = 'resume-parsing::group'; // Redis stream group for resume parsing jobs

/**
 * Get the appropriate message based on parsing status
 */
function getMessage(skipParsing: boolean, parseIntoProfile: boolean): string {
  if (skipParsing) {
    return 'Resume uploaded. It was already parsed previously.';
  }

  if (parseIntoProfile) {
    return 'Resume uploaded and parsing started. Profile will be updated with resume data.';
  }

  return 'Resume uploaded successfully.';
}

export const POST: RequestHandler = async ({ request, locals }) => {
  const user = locals.user;
  console.log('User in resume upload:', user);
  if (!user) return new Response('Unauthorized', { status: 401 });

  // Ensure user has an id
  if (!user.id) {
    console.error('User missing ID:', user);
    return new Response('User ID missing', { status: 400 });
  }

  const formData = await request.formData();
  const file = formData.get('file') as File;
  const profileId = formData.get('profileId') as string;
  let label = (formData.get('label') as string) || file.name;
  const documentType = (formData.get('type') as string) || 'resume';
  const parseIntoProfileStr = formData.get('parseIntoProfile') as string;
  const parseIntoProfile = parseIntoProfileStr === 'true';

  if (!file) {
    return new Response('Missing file', { status: 400 });
  }

  // Only validate profile if profileId is provided
  if (profileId) {
    const profile = await prisma.profile.findUnique({ where: { id: profileId } });
    if (!profile) {
      return new Response('Invalid profileId', { status: 404 });
    }
  }

  // Ensure the document name is unique
  label = await ensureUniqueDocumentName(label, user.id, documentType);

  try {
    // Check if the user has reached their resume creation limit
    // In development mode, we'll bypass this check
    const isDev =
      process.env.NODE_ENV === 'development' || process.env.VITE_DISABLE_FEATURE_LIMITS === 'true';

    if (!isDev) {
      const canCreate = await canCreateResume(user.id);
      if (!canCreate) {
        return new Response(
          JSON.stringify({
            error: 'Document limit reached',
            limitReached: true,
            message:
              'You have reached your document upload limit. Please upgrade your plan to upload more documents.',
          }),
          {
            status: 403,
            headers: { 'Content-Type': 'application/json' },
          }
        );
      }
    } else {
      console.log('Development mode: Bypassing document limit check in resume upload API');
    }

    // Upload the document file
    console.log('Uploading resume:', {
      fileName: file.name,
      fileType: file.type,
      fileSize: file.size,
      documentType,
    });

    // Upload the file to Cloudflare R2
    const uploadResult = await uploadDocumentToR2(file, 'resume', user.id);
    console.log('R2 upload result:', uploadResult);

    if (!uploadResult.success) {
      return new Response(
        JSON.stringify({
          error: 'File upload failed',
          details: uploadResult.error,
        }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }
    // First create a Document record
    console.log('Creating document with data:', {
      label,
      fileUrl: uploadResult.publicPath,
      userId: user.id,
      profileId: profileId || null,
      type: documentType,
    });

    console.log('User ID:', user.id);

    const document = await prisma.document.create({
      data: {
        label,
        fileUrl: uploadResult.publicPath,
        filePath: uploadResult.filePath,
        fileName: uploadResult.originalFileName,
        type: documentType,
        contentType: uploadResult.contentType,
        fileSize: uploadResult.fileSize,
        storageType: 'r2',
        storageLocation: 'resumes',
        // Note: 'source' field is not in the Prisma schema, so we can't set it here
        userId: user.id,
        ...(profileId ? { profileId } : {}),
      },
    });

    console.log('Document created:', document);

    // Then create a Resume record linked to the Document
    const resume = await prisma.resume.create({
      data: {
        documentId: document.id,
      },
      include: {
        document: true,
      },
    });

    console.log('Resume created:', resume);

    // Track resume creation for feature usage
    await trackResumeCreation(user.id);

    // Only send to parsing queue if parseIntoProfile is true
    let skipParsing = false;

    if (parseIntoProfile) {
      // Check if the resume is already parsed
      const existingResume = await prisma.resume.findUnique({
        where: { id: resume.id },
        select: { isParsed: true, parsedAt: true, parsedData: true },
      });

      console.log('Existing resume parse status:', existingResume);

      // If the resume is already parsed and has data, we can skip parsing
      if (existingResume?.isParsed && existingResume?.parsedData) {
        console.log('Resume is already parsed, skipping parsing process');
        skipParsing = true;
      }

      // Only proceed with parsing if we're not skipping it
      if (!skipParsing) {
        // Push the job to Redis to process the resume later
        const jobId = uuid(); // Unique job ID for the processing task

        try {
          // Create job data in the format expected by the worker
          const jobData = {
            jobId,
            resumeId: resume.id,
            filePath: uploadResult.filePath, // Use the filePath directly from the upload result
            fileUrl: uploadResult.publicPath,
            userId: user.id,
            profileId: profileId || '',
            timestamp: new Date().toISOString(),
          };

          // Create stream group if it doesn't exist
          try {
            await RedisConnection.xgroup('CREATE', JOB_STREAM, JOB_GROUP, '$', 'MKSTREAM');
            console.log(`Created ${JOB_STREAM} stream group`);
          } catch (err: any) {
            // Ignore BUSYGROUP error (group already exists)
            if (!err.message.includes('BUSYGROUP')) {
              console.error('Error creating stream group:', err);
            }
          }

          // Add job to Redis stream
          await RedisConnection.xadd(
            JOB_STREAM,
            '*', // Use '*' for auto-generated ID
            'job',
            JSON.stringify(jobData)
          );
          console.log(`Parsing job added to stream '${JOB_STREAM}':`, jobId);

          // Update the resume to mark it as not parsed yet (in case of re-parsing)
          await prisma.resume.update({
            where: { id: resume.id },
            data: {
              isParsed: false,
              parsedAt: null,
            },
          });

          console.log('Resume updated to reset parse status');
        } catch (redisError) {
          console.error('Error adding job to Redis queue:', redisError);
          // Continue without throwing - we'll still return the resume to the user
        }
      }
    } else {
      console.log('Skipping parsing as parseIntoProfile is false');
    }

    // Determine the source based on document properties
    const source = determineDocumentSource(document);

    // Respond to the client
    // Add source information to the response
    const responseData = {
      resume,
      document: {
        ...document,
        source, // Add the determined source information for the frontend
      },
      parseIntoProfile, // Include the parseIntoProfile flag
      profileId: profileId || null, // Include the profileId
      alreadyParsed: skipParsing, // Include whether the resume was already parsed
      message: getMessage(skipParsing, parseIntoProfile),
    };

    return new Response(JSON.stringify(responseData), {
      headers: { 'Content-Type': 'application/json' },
    });
  } catch (error) {
    console.error('Error creating resume:', error);

    // Check if it's a file type error
    if (error?.message?.includes('File type')) {
      return new Response(
        JSON.stringify({
          error: 'Invalid file type',
          details: error.message,
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }

    return new Response(
      JSON.stringify({
        error: 'Failed to create resume',
        details: error.message,
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
};
