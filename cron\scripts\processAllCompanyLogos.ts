// Process ALL companies without logos
import { prisma } from "../lib/prismaClient";
import { downloadAndProcessImage } from "../lib/storage/imageProcessor";
import { chromium } from "playwright";
import { handleCaptchaIfPresent } from "../scripts/improvedCaptchaSolver";
import dotenv from "dotenv";
import path from "path";

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), '.env') });

async function processAllCompanyLogos() {
  console.log("🚀 Processing ALL companies without logos...");
  
  try {
    // Get ALL companies that need logos
    const companies = await prisma.company.findMany({
      where: {
        website: { not: null },
        logoUrl: null,
      },
      select: {
        id: true,
        name: true,
        website: true,
        domain: true,
      },
    });

    console.log(`📊 Found ${companies.length} companies that need logos`);
    
    if (companies.length === 0) {
      console.log("✅ All companies already have logos!");
      return { success: true, processed: 0 };
    }

    let logosAdded = 0;
    let errorCount = 0;
    let skippedCount = 0;

    // Launch browser once for all companies
    const browser = await chromium.launch({ headless: true });

    for (let i = 0; i < companies.length; i++) {
      const company = companies[i];
      console.log(`\n[${i + 1}/${companies.length}] Processing: ${company.name}`);
      
      try {
        // Try to extract logo from website
        let logoUrl: string | null = null;
        
        try {
          const page = await browser.newPage();
          await page.goto(company.website!, { waitUntil: "domcontentloaded", timeout: 15000 });
          
          // Handle captcha if present
          await handleCaptchaIfPresent(page);
          
          // Look for logo in various selectors
          const logoSelectors = [
            'img[alt*="logo" i]',
            'img[class*="logo" i]',
            'img[id*="logo" i]',
            '.logo img',
            '#logo img',
            'header img',
            '.header img',
            '.navbar img',
            '.nav img',
            'img[src*="logo" i]',
          ];

          for (const selector of logoSelectors) {
            const logoElement = await page.$(selector);
            if (logoElement) {
              logoUrl = await logoElement.getAttribute("src");
              if (logoUrl) {
                // Convert relative URLs to absolute
                if (logoUrl.startsWith("//")) {
                  logoUrl = "https:" + logoUrl;
                } else if (logoUrl.startsWith("/")) {
                  const baseUrl = new URL(company.website!);
                  logoUrl = baseUrl.origin + logoUrl;
                } else if (!logoUrl.startsWith("http")) {
                  const baseUrl = new URL(company.website!);
                  logoUrl = baseUrl.origin + "/" + logoUrl;
                }
                break;
              }
            }
          }
          
          await page.close();
          console.log(`   🎨 Extracted logo: ${logoUrl || 'None found'}`);
          
        } catch (error) {
          console.log(`   ⚠️ Website extraction failed: ${error}`);
        }

        // Fallback to Clearbit if no logo found
        if (!logoUrl && company.domain) {
          logoUrl = `https://logo.clearbit.com/${company.domain}`;
          console.log(`   🔄 Using Clearbit fallback: ${logoUrl}`);
        }

        if (!logoUrl) {
          console.log(`   ❌ No logo source found for ${company.name}`);
          skippedCount++;
          continue;
        }

        // Download and process the logo
        const fileName = `${company.name.replace(/[^a-zA-Z0-9]/g, "-").toLowerCase()}-logo`;
        
        const processResult = await downloadAndProcessImage(
          logoUrl,
          fileName,
          company.id
        );

        if (processResult.success && processResult.optimized?.publicUrl) {
          // Update company with logo URL
          await prisma.company.update({
            where: { id: company.id },
            data: {
              logoUrl: processResult.optimized.publicUrl,
            },
          });

          console.log(`   ✅ Logo added: ${processResult.optimized.publicUrl}`);
          logosAdded++;
        } else {
          console.log(`   ❌ Logo processing failed: ${processResult.error}`);
          errorCount++;
        }

      } catch (error) {
        console.log(`   ❌ Error processing ${company.name}: ${error}`);
        errorCount++;
      }

      // Progress update every 10 companies
      if ((i + 1) % 10 === 0) {
        console.log(`\n📊 Progress: ${i + 1}/${companies.length} | Added: ${logosAdded} | Errors: ${errorCount} | Skipped: ${skippedCount}`);
      }
    }

    await browser.close();

    console.log(`\n🎉 Processing complete!`);
    console.log(`   📊 Total companies processed: ${companies.length}`);
    console.log(`   ✅ Logos successfully added: ${logosAdded}`);
    console.log(`   ❌ Errors: ${errorCount}`);
    console.log(`   ⏭️ Skipped: ${skippedCount}`);

    return { success: true, processed: logosAdded };

  } catch (error) {
    console.error("❌ Processing failed:", error);
    return { success: false, processed: 0 };
  } finally {
    await prisma.$disconnect();
  }
}

processAllCompanyLogos()
  .then((result) => {
    if (result.success) {
      console.log(`\n🎉 Successfully processed ${result.processed} company logos!`);
      process.exit(0);
    } else {
      console.log("\n❌ Processing failed!");
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error("💥 Unexpected error:", error);
    process.exit(1);
  });
