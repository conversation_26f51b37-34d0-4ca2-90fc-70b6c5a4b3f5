# Cloudflare R2 Migration Status

## 🎯 **Migration Overview**

This document tracks the migration of all file storage operations from local storage to Cloudflare R2, following the same simple format as company logos (files stored in bucket root).

## ✅ **Completed Migrations**

### 1. **Company Logos** ✅
- **Status**: ✅ **COMPLETE**
- **Location**: `cron/lib/storage/imageProcessor.ts`
- **Storage**: Root of `auto-apply-company` bucket
- **Format**: `company-logo-timestamp.webp`

### 2. **R2 Storage Infrastructure** ✅
- **Status**: ✅ **COMPLETE**
- **Location**: `cron/lib/storage/r2Storage.ts`
- **Features**:
  - Multi-bucket support (company, resumes, user, jobs)
  - File type validation
  - Automatic bucket creation
  - Public URL generation
  - Upload/download/delete operations

### 3. **Resume Upload API** ✅
- **Status**: ✅ **COMPLETE**
- **Location**: `web/src/routes/api/resume/upload/+server.ts`
- **Changes**:
  - Uses `uploadDocumentToR2()` instead of local storage
  - Sets `storageType: 'r2'` in database
  - Stores files in root of `auto-apply-resumes` bucket
  - Format: `resume-name-timestamp.pdf`

### 4. **R2 Document Upload Utility** ✅
- **Status**: ✅ **COMPLETE**
- **Location**: `web/src/lib/utils/r2DocumentUpload.ts`
- **Features**:
  - Document type mapping to R2 file types
  - File validation (size, type)
  - Error handling
  - Download functionality for workers

### 5. **Resume Worker File Handler** ✅
- **Status**: ✅ **COMPLETE**
- **Location**: `workers/resume/r2FileHandler.ts`
- **Features**:
  - Smart file path detection (local vs R2)
  - R2 file download for parsing
  - Temporary file creation for legacy parsers
  - Cleanup functions

### 6. **Environment Variables** ✅
- **Status**: ✅ **COMPLETE**
- **Location**: `render.yaml`
- **Added R2 config to web service**:
  - `R2_ACCOUNT_ID`
  - `R2_ENDPOINT`
  - `R2_ACCESS_KEY_ID`
  - `R2_SECRET_ACCESS_KEY`
  - `R2_CUSTOM_DOMAIN`

## 🔄 **Bucket Configuration**

### **Bucket Structure** (Simple Format)
```
auto-apply-company/          # Company data
├── company-logo-123.webp    # Company logos (root)
└── data/                    # Company data files

auto-apply-resumes/          # Resume storage
├── resume-john-doe-456.pdf  # Resumes (root)
└── (no subfolders)

auto-apply-user/             # User files
├── avatars/                 # Profile pictures
└── documents/               # User documents

auto-apply-jobs/             # Job-related files
├── screenshots/             # Job screenshots
└── assets/                  # Job assets
```

## 📋 **Next Steps Required**

### 1. **Update Resume Parsing Workers** 🔄
- **Files to Update**:
  - `workers/resume/index.ts`
  - `workers/resume/enhanced-parser.ts`
  - `workers/parse-resume.ts`
- **Changes Needed**:
  - Import and use `r2FileHandler.ts`
  - Replace direct file reads with `resolveFileForParsing()`
  - Handle both local and R2 files

### 2. **Run Migration Script** 🔄
- **Script**: `cron/scripts/migrateToR2.ts`
- **Purpose**: Migrate existing local files to R2
- **Process**:
  1. Find all documents with `storageType: 'local'`
  2. Upload files to appropriate R2 buckets
  3. Update database records to `storageType: 'r2'`

### 3. **Update Other Document APIs** 🔄
- **Files**: Any other document upload endpoints
- **Changes**: Use `uploadDocumentToR2()` instead of local storage

### 4. **Test Migration** 🔄
- **Test new resume uploads** → R2 storage
- **Test resume parsing** → R2 file download
- **Test existing resumes** → Migration script
- **Test company logo processing** → Already working

## 🚫 **Excluded from Migration**

### **ONET Data Downloads** ❌ **SKIPPED**
- **Files**: 
  - `cron/lib/onet/downloadAlternateOccupations.ts`
  - `scraper/lib/onet/downloadTechSkills.ts`
- **Reason**: Per user request, ignoring ONET data storage

## 🎯 **Migration Commands**

### **Run Migration Script**
```bash
cd cron
npx tsx scripts/migrateToR2.ts
```

### **Test New Resume Upload**
```bash
# Upload a new resume through the web interface
# Should automatically use R2 storage
```

### **Verify R2 Storage**
```bash
cd cron
npx tsx scripts/testR2Connection.ts  # (if needed)
```

## 📊 **Expected Results**

After completing the migration:

1. **New resumes** → Stored directly in R2
2. **Existing resumes** → Migrated to R2, database updated
3. **Resume parsing** → Downloads from R2, creates temp files
4. **Company logos** → Already working with R2
5. **All file operations** → Use Cloudflare R2 instead of local storage

## 🔧 **Troubleshooting**

### **Common Issues**
1. **File not found errors** → Check `r2FileHandler.ts` path resolution
2. **Upload failures** → Verify R2 credentials in environment
3. **Parsing errors** → Ensure temporary file cleanup is working
4. **Permission errors** → Check R2 token has full permissions

### **Verification Steps**
1. Check database: `storageType` should be `'r2'`
2. Check R2 buckets: Files should appear in Cloudflare dashboard
3. Check public URLs: Should be accessible via R2 endpoints
4. Check parsing: Workers should download and process R2 files

## 🎉 **Benefits**

1. **Scalability** → No local storage limits
2. **Reliability** → Cloudflare's global CDN
3. **Performance** → Fast file access worldwide
4. **Consistency** → All files in one storage system
5. **Cost-effective** → R2's competitive pricing
