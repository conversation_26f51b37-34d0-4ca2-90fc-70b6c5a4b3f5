/**
 * Server hooks for SvelteKit
 *
 * This file contains hooks that run on the server
 * to handle requests and responses.
 */
// WebSocket server is now integrated in server.ts
import { verifySessionToken } from './lib/server/auth';
import { startNotificationWorker } from './lib/server/notification-triggers/notification-worker';
import { subscribeToResumeParsingUpdates } from './lib/server/resume-parsing-subscriber';
import { initWorkerProcessSubscriber } from './lib/server/worker-process-subscriber';
// We don't need the logger in this file
import type { Handle } from '@sveltejs/kit';
import { sequence } from '@sveltejs/kit/hooks';
import { handle as authHandle } from '$lib/auth';
import { dev } from '$app/environment';

// Custom authentication handler
const customHandle: Handle = async ({ event, resolve }) => {
  // Check authentication for protected routes (dashboard pages and protected API routes)
  const pathname = event.url.pathname;

  // Skip authentication for specific API paths
  if (
    pathname.startsWith('/api/occupations') ||
    pathname.startsWith('/api/locations') ||
    pathname.startsWith('/api/auth') ||
    pathname.startsWith('/api/webhooks') ||
    pathname.startsWith('/api/graphql') ||
    pathname.startsWith('/api/health') ||
    pathname === '/api/email/worker' ||
    pathname === '/api/email/worker/metrics' ||
    pathname === '/api/ws' || // Allow WebSocket connections without authentication
    pathname === '/ws' || // Allow the new WebSocket endpoint without authentication
    (pathname.startsWith('/api/jobs') &&
      !pathname.includes('/save') &&
      !pathname.includes('/is-saved')) ||
    pathname.startsWith('/api/search/global') ||
    pathname.startsWith('/api/companies') // Allow public access to companies API for homepage
  ) {
    // Continue with the request without authentication
    const response = await resolve(event, {
      transformPageChunk: ({ html }) => html,
    });
    return response;
  }

  // For dashboard routes and protected API routes, check authentication
  if (pathname.startsWith('/dashboard') || pathname.startsWith('/api/')) {
    // Get the token from cookies
    const token = event.cookies.get('auth_token');

    // If we have a token, verify it and set the user in locals
    if (token) {
      try {
        const user = await verifySessionToken(token);

        if (user) {
          // Set the user in locals with required fields
          event.locals.user = {
            id: user.id,
            email: user.email,
            name: user.name,
            image: user.image,
            passwordHash: (user as any).passwordHash ?? '',
            role: user.role,
            seats: (user as any).seats ?? 1,
            stripeCustomerId: (user as any).stripeCustomerId ?? '',
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
            preferences: (user as any).preferences ?? {},
            emailVerified: (user as any).emailVerified ?? null,
            provider: (user as any).provider ?? '',
            providerId: (user as any).providerId ?? '',
            verificationExpires: (user as any).verificationExpires ?? null,
            verificationToken: (user as any).verificationToken ?? '',
            isAdmin: user.isAdmin || user.role === 'admin',
          };
        } else {
          // console.log(`Invalid token for ${pathname}`);
          // No valid user
        }
        if (!user) {
          if (pathname.startsWith('/api/')) {
            // For API routes, return 401 Unauthorized
            return new Response(JSON.stringify({ error: 'Unauthorized' }), {
              status: 401,
              headers: { 'Content-Type': 'application/json' },
            });
          } else if (!pathname.startsWith('/auth/sign-in')) {
            // For dashboard routes, redirect to sign-in
            console.log(`Redirecting to sign-in from ${pathname} due to invalid token`);
            return new Response(null, {
              status: 303,
              headers: { Location: '/auth/sign-in' },
            });
          }
        }
      } catch (error) {
        console.error(`Error verifying token for ${pathname}:`, error);

        // Handle token verification error
        if (pathname.startsWith('/api/')) {
          return new Response(
            JSON.stringify({ error: 'Unauthorized', details: 'Token verification failed' }),
            {
              status: 401,
              headers: { 'Content-Type': 'application/json' },
            }
          );
        } else if (!pathname.startsWith('/auth/sign-in')) {
          console.log(`Redirecting to sign-in from ${pathname} due to token verification error`);
          return new Response(null, {
            status: 303,
            headers: { Location: '/auth/sign-in' },
          });
        }
      }
    } else {
      console.log(`No token for ${pathname}`);

      // No token
      if (pathname.startsWith('/api/')) {
        // For API routes, return 401 Unauthorized
        return new Response(
          JSON.stringify({ error: 'Unauthorized', details: 'No token provided' }),
          {
            status: 401,
            headers: { 'Content-Type': 'application/json' },
          }
        );
      } else if (!pathname.startsWith('/auth/sign-in')) {
        // For dashboard routes, redirect to sign-in
        console.log(`Redirecting to sign-in from ${pathname} due to missing token`);
        return new Response(null, {
          status: 303,
          headers: { Location: '/auth/sign-in' },
        });
      }
    }
  }

  // Continue with the request
  const response = await resolve(event, {
    transformPageChunk: ({ html }) => html,
  });

  return response;
};

// Combine Auth.js handler with our custom handler
export const handle = sequence(authHandle, customHandle);

/**
 * This function runs when the server starts
 */
export function handleStart({ server }: { server: any }) {
  // Add global error handler for unhandled Redis errors
  process.on('unhandledRejection', (reason: any) => {
    if (reason?.toString().includes('Redis')) {
      if (dev) {
        console.warn('Redis error in development mode (safe to ignore):', reason);
      } else {
        console.error('Redis error in production:', reason);
      }
    } else if (reason?.toString().includes('Not found: /.env')) {
      console.log('Ignored /.env not found error (expected in production)');
    } else {
      console.error('Unhandled promise rejection:', reason);
    }
  });

  // WebSocket server is now integrated in server.ts
  if (server) {
    console.log('Server initialization...');
    try {
      // Set up error handler for server
      server.on('error', (error: Error) => {
        console.error('Server error:', error);
      });

      // Start notification worker if not in development mode or if Redis is available
      console.log('Starting notification worker...');
      startNotificationWorker()
        .then(() => {
          console.log('Notification worker started successfully');
        })
        .catch((error) => {
          if (dev) {
            console.warn(
              'Failed to start notification worker in development mode, continuing without it'
            );
          } else {
            console.error('Failed to start notification worker:', error);
          }
        });

      // Subscribe to resume parsing updates with retry mechanism
      console.log('Subscribing to resume parsing updates...');
      const subscribeWithRetry = async (retryCount = 0, maxRetries = 3) => {
        try {
          await subscribeToResumeParsingUpdates();
          console.log('Successfully subscribed to resume parsing updates');
        } catch (error) {
          if (retryCount < maxRetries) {
            console.warn(
              `Retry ${retryCount + 1}/${maxRetries} for resume parsing subscription...`
            );
            setTimeout(() => subscribeWithRetry(retryCount + 1, maxRetries), 5000);
          } else {
            if (dev) {
              console.warn(
                'Failed to subscribe to resume parsing updates in development mode, continuing without it'
              );
            } else {
              console.error('Failed to subscribe to resume parsing updates:', error);
            }
          }
        }
      };

      // Start subscription with retry mechanism
      subscribeWithRetry();

      // Initialize worker process subscriber
      console.log('Initializing worker process subscriber...');
      initWorkerProcessSubscriber()
        .then(() => {
          console.log('Worker process subscriber initialized successfully');
        })
        .catch((error) => {
          if (dev) {
            console.warn(
              'Failed to initialize worker process subscriber in development mode, continuing without it'
            );
          } else {
            console.error('Failed to initialize worker process subscriber:', error);
          }
        });
    } catch (error) {
      console.error('Failed to initialize WebSocket server:', error);
    }
  } else {
    console.warn('No server instance provided to handleStart');
  }
}
