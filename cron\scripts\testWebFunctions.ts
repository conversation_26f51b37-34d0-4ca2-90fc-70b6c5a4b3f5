// cron/scripts/testWebFunctions.ts
// Test script to verify web functions: company fetching and hero fetchJobs

import { logger } from "../utils/logger";
import dotenv from "dotenv";
import path from "path";
import fetch from "node-fetch";

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), ".env") });

// Configuration
const WEB_BASE_URL = process.env.WEB_BASE_URL || "http://localhost:3000";

interface TestResult {
  name: string;
  success: boolean;
  data?: any;
  error?: string;
  duration: number;
}

/**
 * Test the companies API endpoint
 */
async function testCompaniesAPI(): Promise<TestResult> {
  const startTime = Date.now();
  const testName = "Companies API";

  try {
    logger.info("🧪 Testing companies API...");
    logger.info(`📍 URL: ${WEB_BASE_URL}/api/companies?limit=10`);

    const response = await fetch(`${WEB_BASE_URL}/api/companies?limit=10`, {
      method: "GET",
      headers: {
        Accept: "application/json",
        "User-Agent": "Test-Script/1.0",
      },
    });

    const duration = Date.now() - startTime;

    logger.info(
      `📊 Response status: ${response.status} ${response.statusText}`
    );
    logger.info(
      `📊 Response headers: ${JSON.stringify(Object.fromEntries(response.headers.entries()))}`
    );

    if (!response.ok) {
      const errorText = await response.text();
      logger.error(`❌ Error response body: ${errorText}`);

      return {
        name: testName,
        success: false,
        error: `HTTP ${response.status}: ${response.statusText} - ${errorText}`,
        duration,
      };
    }

    const data = await response.json();

    logger.info(
      `✅ Companies API responded with ${data.length || 0} companies`
    );

    return {
      name: testName,
      success: true,
      data: {
        count: data.length || 0,
        sample: data.slice(0, 3), // First 3 companies as sample
      },
      duration,
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    logger.error(`❌ Companies API test failed:`, error);

    return {
      name: testName,
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      duration,
    };
  }
}

/**
 * Test the featured companies API endpoint
 */
async function testFeaturedCompaniesAPI(): Promise<TestResult> {
  const startTime = Date.now();
  const testName = "Featured Companies API";

  try {
    logger.info("🧪 Testing featured companies API...");

    const response = await fetch(`${WEB_BASE_URL}/api/companies/featured`);
    const duration = Date.now() - startTime;

    if (!response.ok) {
      return {
        name: testName,
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`,
        duration,
      };
    }

    const data = await response.json();

    const totalCompanies =
      (data.startups?.length || 0) +
      (data.growth?.length || 0) +
      (data.enterprise?.length || 0);

    logger.info(
      `✅ Featured companies API responded with ${totalCompanies} companies across categories`
    );
    logger.info(`   - Startups: ${data.startups?.length || 0}`);
    logger.info(`   - Growth: ${data.growth?.length || 0}`);
    logger.info(`   - Enterprise: ${data.enterprise?.length || 0}`);

    return {
      name: testName,
      success: true,
      data: {
        categories: {
          startups: data.startups?.length || 0,
          growth: data.growth?.length || 0,
          enterprise: data.enterprise?.length || 0,
        },
        totalCompanies,
        sampleStartup: data.startups?.[0],
        sampleGrowth: data.growth?.[0],
        sampleEnterprise: data.enterprise?.[0],
      },
      duration,
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    logger.error(`❌ Featured companies API test failed:`, error);

    return {
      name: testName,
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      duration,
    };
  }
}

/**
 * Test the jobs API endpoint (used by hero section)
 */
async function testJobsAPI(): Promise<TestResult> {
  const startTime = Date.now();
  const testName = "Jobs API (Hero Section)";

  try {
    logger.info("🧪 Testing jobs API for hero section...");

    const response = await fetch(
      `${WEB_BASE_URL}/api/jobs?limit=200&random=true`
    );
    const duration = Date.now() - startTime;

    if (!response.ok) {
      return {
        name: testName,
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`,
        duration,
      };
    }

    const data = await response.json();
    const jobs = data.jobs || [];

    logger.info(`✅ Jobs API responded with ${jobs.length} jobs`);

    // Analyze job data structure
    if (jobs.length > 0) {
      const firstJob = jobs[0];
      const availableFields = Object.keys(firstJob);

      logger.info(`   - Available fields: ${availableFields.join(", ")}`);
      logger.info(`   - Sample job title: ${firstJob.title || "N/A"}`);
      logger.info(
        `   - Sample company: ${firstJob.company?.name || firstJob.companyName || "N/A"}`
      );
      logger.info(`   - Has description: ${!!firstJob.description}`);
    }

    return {
      name: testName,
      success: true,
      data: {
        count: jobs.length,
        sampleJob: jobs[0],
        availableFields: jobs.length > 0 ? Object.keys(jobs[0]) : [],
        hasCompanyData:
          jobs.length > 0 && (jobs[0].company || jobs[0].companyName),
        hasDescriptions: jobs.filter((job) => job.description).length,
      },
      duration,
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    logger.error(`❌ Jobs API test failed:`, error);

    return {
      name: testName,
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      duration,
    };
  }
}

/**
 * Test GraphQL endpoint for companies
 */
async function testGraphQLCompanies(): Promise<TestResult> {
  const startTime = Date.now();
  const testName = "GraphQL Companies Query";

  try {
    logger.info("🧪 Testing GraphQL companies query...");

    const query = `
      query GetCompanies($search: String, $limit: Int) {
        companies(search: $search, limit: $limit) {
          id
          name
          domain
          logoUrl
        }
      }
    `;

    const response = await fetch(`${WEB_BASE_URL}/api/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        query,
        variables: { limit: 10 },
      }),
    });

    const duration = Date.now() - startTime;

    if (!response.ok) {
      return {
        name: testName,
        success: false,
        error: `HTTP ${response.status}: ${response.statusText}`,
        duration,
      };
    }

    const data = await response.json();

    if (data.errors) {
      return {
        name: testName,
        success: false,
        error: `GraphQL errors: ${JSON.stringify(data.errors)}`,
        duration,
      };
    }

    const companies = data.data?.companies || [];

    logger.info(
      `✅ GraphQL companies query responded with ${companies.length} companies`
    );

    return {
      name: testName,
      success: true,
      data: {
        count: companies.length,
        sample: companies.slice(0, 3),
      },
      duration,
    };
  } catch (error) {
    const duration = Date.now() - startTime;
    logger.error(`❌ GraphQL companies test failed:`, error);

    return {
      name: testName,
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      duration,
    };
  }
}

/**
 * Main test function
 */
async function runWebFunctionTests() {
  logger.info("🚀 Starting web functions test suite");
  logger.info(`📍 Testing against: ${WEB_BASE_URL}`);
  logger.info("");
  logger.info("🔍 ANALYSIS:");
  logger.info(
    "Based on hooks.server.ts, the following API endpoints are PUBLIC (no auth required):"
  );
  logger.info("  ✅ /api/jobs (except /save and /is-saved) - SHOULD WORK");
  logger.info("  ✅ /api/graphql - SHOULD WORK");
  logger.info("  ✅ /api/companies - SHOULD WORK (added to exception list)");
  logger.info(
    "  ✅ /api/companies/featured - SHOULD WORK (added to exception list)"
  );
  logger.info("  ⚠️  NOTE: Web server may need restart to pick up hooks.server.ts changes");
  logger.info("");

  const tests = [
    testCompaniesAPI,
    testFeaturedCompaniesAPI,
    testJobsAPI,
    testGraphQLCompanies,
  ];

  const results: TestResult[] = [];

  for (const test of tests) {
    try {
      const result = await test();
      results.push(result);

      if (result.success) {
        logger.info(`✅ ${result.name} - PASSED (${result.duration}ms)`);
      } else {
        logger.error(
          `❌ ${result.name} - FAILED (${result.duration}ms): ${result.error}`
        );
      }

      // Add delay between tests
      await new Promise((resolve) => setTimeout(resolve, 1000));
    } catch (error) {
      logger.error(`💥 Test execution failed for ${test.name}:`, error);
      results.push({
        name: test.name || "Unknown Test",
        success: false,
        error: error instanceof Error ? error.message : "Test execution failed",
        duration: 0,
      });
    }
  }

  // Summary
  const passed = results.filter((r) => r.success).length;
  const failed = results.filter((r) => !r.success).length;
  const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);

  logger.info("\n📊 TEST SUMMARY");
  logger.info("================");
  logger.info(`Total tests: ${results.length}`);
  logger.info(`Passed: ${passed}`);
  logger.info(`Failed: ${failed}`);
  logger.info(`Total duration: ${totalDuration}ms`);

  if (failed > 0) {
    logger.info("\n❌ FAILED TESTS:");
    results
      .filter((r) => !r.success)
      .forEach((result) => {
        logger.info(`   - ${result.name}: ${result.error}`);
      });
  }

  if (passed > 0) {
    logger.info("\n✅ SUCCESSFUL TESTS:");
    results
      .filter((r) => r.success)
      .forEach((result) => {
        logger.info(`   - ${result.name} (${result.duration}ms)`);
        if (result.data) {
          logger.info(
            `     Data: ${JSON.stringify(result.data, null, 2).substring(0, 200)}...`
          );
        }
      });
  }

  return {
    total: results.length,
    passed,
    failed,
    results,
    success: failed === 0,
  };
}

// Run tests if this script is executed directly
// For ES modules, we'll just run the tests directly
runWebFunctionTests()
  .then((summary) => {
    if (summary.success) {
      logger.info("🎉 All tests passed!");
      process.exit(0);
    } else {
      logger.error("💥 Some tests failed!");
      process.exit(1);
    }
  })
  .catch((error) => {
    logger.error("💥 Test suite failed:", error);
    process.exit(1);
  });

export { runWebFunctionTests };
