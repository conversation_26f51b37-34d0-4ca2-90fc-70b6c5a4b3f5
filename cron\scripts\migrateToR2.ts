// cron/scripts/migrateToR2.ts
// Migration script to move existing local files to Cloudflare R2

import { prisma } from "../lib/prismaClient";
import { uploadFile, FileType } from "../lib/storage/r2Storage";
import { logger } from "../utils/logger";
import fs from "fs/promises";
import path from "path";
import dotenv from "dotenv";

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), ".env") });

interface MigrationResult {
  success: boolean;
  migrated: number;
  failed: number;
  skipped: number;
  errors: string[];
}

/**
 * Migrate resume documents to R2
 */
async function migrateResumes(): Promise<MigrationResult> {
  logger.info("🚀 Starting resume migration to R2...");
  
  const result: MigrationResult = {
    success: true,
    migrated: 0,
    failed: 0,
    skipped: 0,
    errors: []
  };

  try {
    // Get all documents that are stored locally
    const documents = await prisma.document.findMany({
      where: {
        storageType: "local",
        type: "resume",
        filePath: { not: null }
      },
      include: {
        resumes: true
      }
    });

    logger.info(`📊 Found ${documents.length} resume documents to migrate`);

    for (const document of documents) {
      try {
        logger.info(`📄 Migrating document: ${document.fileName} (ID: ${document.id})`);

        // Check if file exists locally
        const localPath = document.filePath!;
        let fileBuffer: Buffer;

        try {
          fileBuffer = await fs.readFile(localPath);
        } catch (fileError) {
          // Try alternative paths
          const alternativePaths = [
            path.join(process.cwd(), localPath),
            path.join(process.cwd(), "static", localPath),
            path.join(process.cwd(), "web", localPath),
            localPath.replace(/^.*static[/\\]/, "static/"),
            localPath.replace(/^.*uploads[/\\]/, "static/uploads/")
          ];

          let fileFound = false;
          for (const altPath of alternativePaths) {
            try {
              fileBuffer = await fs.readFile(altPath);
              logger.info(`   ✅ Found file at: ${altPath}`);
              fileFound = true;
              break;
            } catch {
              // Continue to next path
            }
          }

          if (!fileFound) {
            logger.error(`   ❌ File not found: ${localPath}`);
            result.failed++;
            result.errors.push(`File not found: ${document.fileName} at ${localPath}`);
            continue;
          }
        }

        // Upload to R2
        const uploadResult = await uploadFile(
          fileBuffer,
          document.fileName,
          document.contentType || "application/pdf",
          "resumes" as FileType,
          document.userId
        );

        if (uploadResult.success) {
          // Update document record
          await prisma.document.update({
            where: { id: document.id },
            data: {
              storageType: "r2",
              storageLocation: "resumes",
              fileUrl: uploadResult.publicUrl,
              filePath: uploadResult.fileKey // Store R2 key as filePath
            }
          });

          logger.info(`   ✅ Migrated successfully: ${uploadResult.publicUrl}`);
          result.migrated++;
        } else {
          logger.error(`   ❌ Upload failed: ${uploadResult.error}`);
          result.failed++;
          result.errors.push(`Upload failed for ${document.fileName}: ${uploadResult.error}`);
        }

      } catch (error) {
        logger.error(`   ❌ Error migrating document ${document.id}:`, error);
        result.failed++;
        result.errors.push(`Error migrating ${document.fileName}: ${error instanceof Error ? error.message : "Unknown error"}`);
      }
    }

    logger.info(`\n📊 Resume migration complete:`);
    logger.info(`   ✅ Migrated: ${result.migrated}`);
    logger.info(`   ❌ Failed: ${result.failed}`);
    logger.info(`   ⏭️ Skipped: ${result.skipped}`);

    return result;

  } catch (error) {
    logger.error("❌ Resume migration failed:", error);
    result.success = false;
    result.errors.push(`Migration failed: ${error instanceof Error ? error.message : "Unknown error"}`);
    return result;
  }
}

/**
 * Migrate user documents to R2
 */
async function migrateUserDocuments(): Promise<MigrationResult> {
  logger.info("🚀 Starting user documents migration to R2...");
  
  const result: MigrationResult = {
    success: true,
    migrated: 0,
    failed: 0,
    skipped: 0,
    errors: []
  };

  try {
    // Get all documents that are stored locally (non-resume documents)
    const documents = await prisma.document.findMany({
      where: {
        storageType: "local",
        type: { not: "resume" },
        filePath: { not: null }
      }
    });

    logger.info(`📊 Found ${documents.length} user documents to migrate`);

    for (const document of documents) {
      try {
        logger.info(`📄 Migrating document: ${document.fileName} (ID: ${document.id})`);

        // Check if file exists locally
        const localPath = document.filePath!;
        let fileBuffer: Buffer;

        try {
          fileBuffer = await fs.readFile(localPath);
        } catch (fileError) {
          // Try alternative paths (same logic as resumes)
          const alternativePaths = [
            path.join(process.cwd(), localPath),
            path.join(process.cwd(), "static", localPath),
            path.join(process.cwd(), "web", localPath),
            localPath.replace(/^.*static[/\\]/, "static/"),
            localPath.replace(/^.*uploads[/\\]/, "static/uploads/")
          ];

          let fileFound = false;
          for (const altPath of alternativePaths) {
            try {
              fileBuffer = await fs.readFile(altPath);
              logger.info(`   ✅ Found file at: ${altPath}`);
              fileFound = true;
              break;
            } catch {
              // Continue to next path
            }
          }

          if (!fileFound) {
            logger.error(`   ❌ File not found: ${localPath}`);
            result.failed++;
            result.errors.push(`File not found: ${document.fileName} at ${localPath}`);
            continue;
          }
        }

        // Upload to R2
        const uploadResult = await uploadFile(
          fileBuffer,
          document.fileName,
          document.contentType || "application/pdf",
          "userDocuments" as FileType,
          document.userId
        );

        if (uploadResult.success) {
          // Update document record
          await prisma.document.update({
            where: { id: document.id },
            data: {
              storageType: "r2",
              storageLocation: "user-documents",
              fileUrl: uploadResult.publicUrl,
              filePath: uploadResult.fileKey // Store R2 key as filePath
            }
          });

          logger.info(`   ✅ Migrated successfully: ${uploadResult.publicUrl}`);
          result.migrated++;
        } else {
          logger.error(`   ❌ Upload failed: ${uploadResult.error}`);
          result.failed++;
          result.errors.push(`Upload failed for ${document.fileName}: ${uploadResult.error}`);
        }

      } catch (error) {
        logger.error(`   ❌ Error migrating document ${document.id}:`, error);
        result.failed++;
        result.errors.push(`Error migrating ${document.fileName}: ${error instanceof Error ? error.message : "Unknown error"}`);
      }
    }

    logger.info(`\n📊 User documents migration complete:`);
    logger.info(`   ✅ Migrated: ${result.migrated}`);
    logger.info(`   ❌ Failed: ${result.failed}`);
    logger.info(`   ⏭️ Skipped: ${result.skipped}`);

    return result;

  } catch (error) {
    logger.error("❌ User documents migration failed:", error);
    result.success = false;
    result.errors.push(`Migration failed: ${error instanceof Error ? error.message : "Unknown error"}`);
    return result;
  }
}

/**
 * Main migration function
 */
async function runMigration() {
  logger.info("🚀 Starting complete migration to Cloudflare R2");
  
  const resumeResult = await migrateResumes();
  const userDocsResult = await migrateUserDocuments();
  
  const totalMigrated = resumeResult.migrated + userDocsResult.migrated;
  const totalFailed = resumeResult.failed + userDocsResult.failed;
  const allErrors = [...resumeResult.errors, ...userDocsResult.errors];
  
  logger.info(`\n🎉 Migration Summary:`);
  logger.info(`   📊 Total migrated: ${totalMigrated}`);
  logger.info(`   ❌ Total failed: ${totalFailed}`);
  
  if (allErrors.length > 0) {
    logger.info(`\n❌ Errors encountered:`);
    allErrors.forEach((error, index) => {
      logger.info(`   ${index + 1}. ${error}`);
    });
  }
  
  if (totalFailed === 0) {
    logger.info("✅ All files migrated successfully!");
    return { success: true, migrated: totalMigrated, failed: 0 };
  } else {
    logger.info(`⚠️ Migration completed with ${totalFailed} failures`);
    return { success: false, migrated: totalMigrated, failed: totalFailed };
  }
}

// Run migration if this script is executed directly
runMigration()
  .then((result) => {
    if (result.success) {
      logger.info("🎉 Migration completed successfully!");
      process.exit(0);
    } else {
      logger.error("💥 Migration completed with errors!");
      process.exit(1);
    }
  })
  .catch((error) => {
    logger.error("💥 Migration failed:", error);
    process.exit(1);
  });

export { runMigration, migrateResumes, migrateUserDocuments };
