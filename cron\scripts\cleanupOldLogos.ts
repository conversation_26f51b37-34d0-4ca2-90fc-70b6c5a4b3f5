// Clean up old logo structure and update database
import { S3Client, ListObjectsV2Command, DeleteObjectCommand } from "@aws-sdk/client-s3";
import { prisma } from "../lib/prismaClient";
import dotenv from "dotenv";
import path from "path";

// Load environment variables
dotenv.config({ path: path.join(process.cwd(), '.env') });

const r2Client = new S3Client({
  endpoint: process.env.R2_ENDPOINT || "https://7efc1bf67e7d23f5683e06d0227c883f.r2.cloudflarestorage.com",
  region: "auto",
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY || "",
  },
});

async function cleanupOldLogos() {
  console.log("🧹 Cleaning up old logo structure...");
  
  try {
    const bucketName = "hirli-company-logos";
    
    // Step 1: List all objects in the bucket
    console.log("\n📋 Step 1: Listing all objects in bucket...");
    const listCommand = new ListObjectsV2Command({
      Bucket: bucketName,
    });
    
    const response = await r2Client.send(listCommand);
    const objects = response.Contents || [];
    
    console.log(`Found ${objects.length} objects in bucket`);
    
    // Step 2: Identify old structure files (those with logos/ prefix)
    const oldStructureFiles = objects.filter(obj => obj.Key?.startsWith('logos/'));
    const newStructureFiles = objects.filter(obj => !obj.Key?.startsWith('logos/'));
    
    console.log(`\n📊 File structure analysis:`);
    console.log(`   Old structure files (logos/*): ${oldStructureFiles.length}`);
    console.log(`   New structure files: ${newStructureFiles.length}`);
    
    if (oldStructureFiles.length > 0) {
      console.log("\n🗑️ Step 2: Deleting old structure files...");
      
      for (const file of oldStructureFiles) {
        if (file.Key) {
          console.log(`   Deleting: ${file.Key}`);
          await r2Client.send(new DeleteObjectCommand({
            Bucket: bucketName,
            Key: file.Key,
          }));
        }
      }
      
      console.log(`✅ Deleted ${oldStructureFiles.length} old structure files`);
    } else {
      console.log("✅ No old structure files found to delete");
    }
    
    // Step 3: Update database to remove old logo URLs
    console.log("\n💾 Step 3: Cleaning up database URLs...");
    
    const companiesWithOldUrls = await prisma.company.findMany({
      where: {
        logoUrl: {
          contains: "/logos/"
        }
      },
      select: {
        id: true,
        name: true,
        logoUrl: true,
      }
    });
    
    console.log(`Found ${companiesWithOldUrls.length} companies with old logo URLs`);
    
    if (companiesWithOldUrls.length > 0) {
      // Clear old logo URLs so they can be re-processed with new structure
      await prisma.company.updateMany({
        where: {
          logoUrl: {
            contains: "/logos/"
          }
        },
        data: {
          logoUrl: null
        }
      });
      
      console.log(`✅ Cleared ${companiesWithOldUrls.length} old logo URLs from database`);
    }
    
    // Step 4: Show current state
    console.log("\n📊 Current state:");
    const totalCompanies = await prisma.company.count();
    const companiesWithLogos = await prisma.company.count({
      where: { logoUrl: { not: null } }
    });
    const companiesWithoutLogos = await prisma.company.count({
      where: { logoUrl: null }
    });
    
    console.log(`   Total companies: ${totalCompanies}`);
    console.log(`   Companies with logos: ${companiesWithLogos}`);
    console.log(`   Companies without logos: ${companiesWithoutLogos}`);
    
    return true;
    
  } catch (error) {
    console.error("❌ Cleanup failed:", error);
    return false;
  } finally {
    await prisma.$disconnect();
  }
}

cleanupOldLogos()
  .then((success) => {
    if (success) {
      console.log("\n🎉 Cleanup completed successfully!");
      console.log("💡 Ready to run company enrichment with new structure");
    } else {
      console.log("\n❌ Cleanup failed!");
    }
    process.exit(success ? 0 : 1);
  })
  .catch((error) => {
    console.error("💥 Unexpected error:", error);
    process.exit(1);
  });
