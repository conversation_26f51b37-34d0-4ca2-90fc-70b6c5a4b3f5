// workers/resume/r2FileHandler.ts
// Utility to handle file operations for R2 storage in resume parsing workers

import fs from 'fs/promises';
import path from 'path';
import { downloadFile, BucketType } from '../../cron/lib/storage/r2Storage';

export interface FileLocation {
  isLocal: boolean;
  isR2: boolean;
  localPath?: string;
  r2Key?: string;
  bucketType?: BucketType;
}

/**
 * Determine if a file path is local or R2 based
 */
export function analyzeFilePath(filePath: string): FileLocation {
  // R2 files will have keys without local path separators and won't start with common local paths
  const isLocalPath = filePath.includes('static/') || 
                     filePath.includes('uploads/') || 
                     filePath.startsWith('/') || 
                     filePath.includes('\\') ||
                     filePath.includes('C:') ||
                     filePath.includes('app/');

  if (isLocalPath) {
    return {
      isLocal: true,
      isR2: false,
      localPath: filePath
    };
  } else {
    // Assume it's an R2 key
    return {
      isLocal: false,
      isR2: true,
      r2Key: filePath,
      bucketType: BucketType.RESUMES // Default to resumes bucket
    };
  }
}

/**
 * Get file buffer from either local storage or R2
 */
export async function getFileBuffer(filePath: string): Promise<{ success: boolean; buffer?: Buffer; error?: string }> {
  const location = analyzeFilePath(filePath);

  if (location.isLocal) {
    return await getLocalFileBuffer(location.localPath!);
  } else if (location.isR2) {
    return await getR2FileBuffer(location.r2Key!, location.bucketType!);
  } else {
    return {
      success: false,
      error: 'Unable to determine file location type'
    };
  }
}

/**
 * Get file buffer from local storage with fallback paths
 */
async function getLocalFileBuffer(filePath: string): Promise<{ success: boolean; buffer?: Buffer; error?: string }> {
  console.log(`[R2FileHandler] Attempting to read local file: ${filePath}`);

  // Try the original path first
  try {
    const buffer = await fs.readFile(filePath);
    console.log(`[R2FileHandler] Successfully read file from: ${filePath}`);
    return { success: true, buffer };
  } catch (error) {
    console.log(`[R2FileHandler] Failed to read from original path: ${filePath}`);
  }

  // Try alternative paths
  const alternativePaths = [
    path.join(process.cwd(), filePath),
    path.join(process.cwd(), "static", filePath),
    path.join(process.cwd(), "web", filePath),
    filePath.replace(/^.*static[/\\]/, "static/"),
    filePath.replace(/^.*uploads[/\\]/, "static/uploads/"),
    // Handle different upload path formats
    filePath.replace(/^\/uploads\//, "static/uploads/"),
    filePath.replace(/^\/assets\//, "static/assets/"),
    filePath.replace(/^\/resumes\//, "static/uploads/resumes/"),
    // Handle Windows paths
    filePath.replace(/\\/g, "/")
  ];

  for (const altPath of alternativePaths) {
    try {
      const buffer = await fs.readFile(altPath);
      console.log(`[R2FileHandler] Successfully read file from alternative path: ${altPath}`);
      return { success: true, buffer };
    } catch (error) {
      // Continue to next path
    }
  }

  return {
    success: false,
    error: `File not found at ${filePath} or any alternative paths`
  };
}

/**
 * Get file buffer from R2 storage
 */
async function getR2FileBuffer(r2Key: string, bucketType: BucketType): Promise<{ success: boolean; buffer?: Buffer; error?: string }> {
  console.log(`[R2FileHandler] Attempting to download from R2: ${r2Key} (bucket: ${bucketType})`);

  try {
    const result = await downloadFile(r2Key, bucketType);
    
    if (result.success && result.buffer) {
      console.log(`[R2FileHandler] Successfully downloaded from R2: ${r2Key} (${result.buffer.length} bytes)`);
      return { success: true, buffer: result.buffer };
    } else {
      console.log(`[R2FileHandler] Failed to download from R2: ${result.error}`);
      return { success: false, error: result.error };
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.log(`[R2FileHandler] Error downloading from R2: ${errorMessage}`);
    return { success: false, error: errorMessage };
  }
}

/**
 * Create a temporary local file from R2 content for legacy parsers
 */
export async function createTempFileFromR2(r2Key: string, bucketType: BucketType): Promise<{ success: boolean; tempPath?: string; error?: string; cleanup?: () => Promise<void> }> {
  try {
    console.log(`[R2FileHandler] Creating temporary file for R2 key: ${r2Key}`);

    // Download from R2
    const downloadResult = await getR2FileBuffer(r2Key, bucketType);
    if (!downloadResult.success || !downloadResult.buffer) {
      return { success: false, error: downloadResult.error };
    }

    // Create temporary file
    const tempDir = path.join(process.cwd(), 'temp');
    await fs.mkdir(tempDir, { recursive: true });

    const tempFileName = `temp-${Date.now()}-${path.basename(r2Key)}`;
    const tempPath = path.join(tempDir, tempFileName);

    await fs.writeFile(tempPath, downloadResult.buffer);

    console.log(`[R2FileHandler] Created temporary file: ${tempPath}`);

    // Return cleanup function
    const cleanup = async () => {
      try {
        await fs.unlink(tempPath);
        console.log(`[R2FileHandler] Cleaned up temporary file: ${tempPath}`);
      } catch (error) {
        console.log(`[R2FileHandler] Failed to cleanup temporary file: ${tempPath}`, error);
      }
    };

    return { success: true, tempPath, cleanup };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.log(`[R2FileHandler] Error creating temporary file: ${errorMessage}`);
    return { success: false, error: errorMessage };
  }
}

/**
 * Smart file resolver that handles both local and R2 files
 * Returns a local file path that can be used by existing parsers
 */
export async function resolveFileForParsing(filePath: string): Promise<{ success: boolean; localPath?: string; error?: string; cleanup?: () => Promise<void> }> {
  const location = analyzeFilePath(filePath);

  if (location.isLocal) {
    // For local files, try to find the actual path
    const result = await getLocalFileBuffer(location.localPath!);
    if (result.success) {
      return { success: true, localPath: location.localPath };
    } else {
      return { success: false, error: result.error };
    }
  } else if (location.isR2) {
    // For R2 files, create a temporary local file
    return await createTempFileFromR2(location.r2Key!, location.bucketType!);
  } else {
    return { success: false, error: 'Unable to determine file location type' };
  }
}

export default {
  analyzeFilePath,
  getFileBuffer,
  createTempFileFromR2,
  resolveFileForParsing
};
